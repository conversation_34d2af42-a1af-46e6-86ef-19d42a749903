import { Spin } from "antd";
import { FC, useContext, useEffect } from "react";
import { Route, Switch, useHistory, useRouteMatch } from "react-router-dom";
import styles from "./index.module.scss";
import RecentlyDesktop from "./Recently";
import RecentlyPlay from "./Recently/RecentlyPlay";
import RecentlyAdd from "./Recently/RecentlyAdd";
import AllByDesktop from "./All";
import CollectByDesktop from "./Collect";
import PlayedByDesktop from "./Played";
import LibraryByDesktop from "./Library";
import VideoDetails from "./All/VideoDetails";
import ActorDetail from "./All/VideoDetails/ActorDetail";
import LibraryManagement from "./LibraryManagement";
import useFATSideBar from "@/layouts/sideBarHooks/fat";
import { useSideBarChange } from "@/layouts/Layout";
import { LibraryContext } from "@/hooks/useVideoLibrary";


const DefaultLayout = () => {
  const history = useHistory();

  useEffect(() => {
    history.push('/filmAndTelevisionWall_pc/recently');
  }, [history])

  return (
    <div className={styles.loadingContainer}>
      <Spin size="large" />
    </div>
  )
}

export const useLibraryList = () => useContext(LibraryContext);

const FilmAndTelevisionWallDesktop: FC = (props) => {
  const { path } = useRouteMatch();

  // 获取侧边栏相关数据
  const { fatComponents, libraries, setLibraries, getLib } = useFATSideBar();

  const { setSidePanel } = useSideBarChange(); // 侧边栏变化

  // 初始化侧边栏
  useEffect(() => {
    setSidePanel(fatComponents);
  }, [fatComponents, setSidePanel])

  return (
    <>
      <LibraryContext.Provider value={{ libs: libraries, setLibs: setLibraries, refreshLibraries: getLib as any }}>
        <div className={styles.root_container}>
          <Switch>
            <Route exact={true} path={`${path}/recently`}>
              <RecentlyDesktop />
            </Route>
            <Route exact path={`${path}/recently/recentlyPlay`}>
              <RecentlyPlay />
            </Route>
            <Route exact path={`${path}/recently/recentlyAdd`}>
              <RecentlyAdd />
            </Route>

            <Route exact path={`${path}/all`}>
              <AllByDesktop />
            </Route>
            <Route exact path={`${path}/collect`}>
              <CollectByDesktop />
            </Route>
            <Route exact path={`${path}/played`}>
              <PlayedByDesktop />
            </Route>
            <Route exact path={`${path}/library/:id`}>
              <LibraryByDesktop />
            </Route>
            <Route exact path={`${path}/all/videoDetails`}>
              <VideoDetails />
            </Route>
            <Route exact path={`${path}/all/videoDetails/actorDetail`}>
              <ActorDetail />
            </Route>
            <Route exact path={`${path}/libraryManagement`}>
              <LibraryManagement />
            </Route>


            {/* 默认内容 */}
            <Route exact={true} path={path}>
              <DefaultLayout />
            </Route>
          </Switch>
        </div>
      </LibraryContext.Provider>
    </>
  )
}

export default FilmAndTelevisionWallDesktop;