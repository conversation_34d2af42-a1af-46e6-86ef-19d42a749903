import { Route, useLocation } from "react-router-dom";
import { useRequest } from "ahooks";
import IPCHome from "./Home";
import NotAdded from "./NotAdded";
import styles from "./index.module.scss";
import { useState, useEffect, useRef, useCallback } from "react";
import { listRecordCamera, CameraInfo } from "@/api/ipc";
import { Toast } from "antd-mobile";
import XMLoading from "@/components/XMLoading";

export default function CameraManagement(props: {
  children?: React.ReactNode;
}) {
  const [hasCameras, setHasCameras] = useState(false);
  const [cameraList, setCameraList] = useState<CameraInfo[]>([]);
  const [isFirst, setIsFirst] = useState(false);
  const location = useLocation<{ shouldRefresh?: boolean }>();
  const isInitialLoad = useRef(true);

  // 快速加载摄像机列表（不包含米家信息）
  const { loading } = useRequest(
    () => listRecordCamera({ did: [], fast_return: true }, { showLoading: false }),
    {
      manual: false,
      onSuccess: (res) => {
        if (res && (res.code !== 0)) {
          Toast.show(res?.result);
          return;
        }
        const cameras = res.data?.camera || [];
        setCameraList(cameras);
        setHasCameras(cameras.length > 0);
        setIsFirst(true);

        // 如果是初始加载且有摄像机，静默获取完整信息
        if (isInitialLoad.current && cameras.length > 0) {
          fetchCameraListComplete();
        }
      },
      onError: (err) => {
        console.error("摄像头列表获取失败:", err);
        setCameraList([]);
        setHasCameras(false);
      },
    }
  );

  // 获取完整摄像机信息（包含米家信息）
  const { run: fetchCameraListComplete } = useRequest(
    () => listRecordCamera({ did: [] }, { showLoading: false }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res && (res.code !== 0)) {
          // 静默失败，不显示错误信息
          return;
        }
        const cameras = res.data?.camera || [];
        setCameraList(cameras);
        setHasCameras(cameras.length > 0);
        isInitialLoad.current = false;
        setIsFirst(false)
      },
      onError: (err) => {
        console.error("获取完整摄像头信息失败:", err);
        // 静默失败，保持当前数据
      },
    }
  );

  // 提供刷新摄像头列表的方法（获取完整信息）
  const refreshCameraList = useCallback(() => {
    isInitialLoad.current = false;
    fetchCameraListComplete();
  }, [fetchCameraListComplete]);

  // 监听路由状态变化，如果需要刷新则重新获取数据
  useEffect(() => {
    if (location.state?.shouldRefresh) {
      refreshCameraList();
    }
  }, [location.state?.shouldRefresh, refreshCameraList]);

  if (loading)
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.loadingContent}>
          <XMLoading loading={true} size={22} />
          <span className={styles.loadingText}>加载中</span>
        </div>
      </div>
    );

  return (
    <div id="cameraManagementContainer" className={styles.cameraManagementContainer}>
      <div className={styles.top}></div>
      <div className={styles.content}>
        {props.children}
        <Route exact path="/cameraManagement_app">
          {hasCameras ? (
            <IPCHome
              cameraList={cameraList}
              refreshCameraList={refreshCameraList}
              isFirst={isFirst}
            />
          ) : (
            <NotAdded />
          )}
        </Route>
      </div>
    </div>
  );
}
