import { PreloadImage } from "@/components/Image";
import NavigatorBar from "@/components/NavBar";
import { FC, useCallback, useContext, useEffect, useMemo, useState } from "react";
import { Route, Switch, useHistory, useRouteMatch } from "react-router-dom";
import searchIcon from "@/Resources/icon/search.png";
import moreIcon from "@/Resources/icon/moreOps.png";
import searchIcon_dark from "@/Resources/icon/search_dark.png";
import moreIcon_dark from "@/Resources/icon/moreOps_dark.png";
import filterIcon from "@/Resources/icon/deviceDetail.png";
import filterIcon_dark from "@/Resources/icon/deviceDetail_dark.png";
import hdmiIcon from "@/Resources/icon/hdmi.png";
import hdmiIcon_dark from "@/Resources/icon/hdmi_dark.png";

import styles from "./index.module.scss";
import Recently from "./Recently";
import { Spin } from "antd";
import { useTheme } from '@/utils/themeDetector';
import All from "./All";
import { Popover } from "antd-mobile";
import { LibraryContext, useVideoLibraryList } from "@/hooks/useVideoLibrary";
import { videoWallHDMIGet, videoWallHDMIOnClick } from "@/api/fatWallJSBridge";

const DefaultLayout = () => {
  const history = useHistory();

  useEffect(() => {
    history.push('/filmAndTelevisionWall_app/recently');
  }, [history])

  return (
    <div className={styles.loadingContainer}>
      <Spin size="large" />
    </div>
  )
}



export const HomePage = (props: { path: string }) => {
  const { path } = props;
  const history = useHistory();
  const [showFloatPanel, setShowFloatPanel] = useState(false);
  const routeMatch = useRouteMatch();
  const prefix = routeMatch.path.split('/')[1];

  const [moreVisible, setMoreVisible] = useState<boolean>(false);

  // 判断是否有hdmi
  const [hasHDMI, setHasHDMI] = useState<boolean>(false);



  const toSearch = useCallback(() => {
    // 清空Search页面的缓存数据
    try {
      // 清空 localStorage 中的搜索状态
      localStorage.removeItem('fatwall_search_state');

      // 清空进入详情页的标记
      localStorage.removeItem('fatwall_entering_detail');

      // 清空搜索返回信息（从主页面进入搜索时应该清除Library的返回信息）
      localStorage.removeItem('fatwall_search_return_info');

      // 清空所有可能的 sessionStorage 搜索状态
      // 遍历 sessionStorage，清除所有以 searchState_ 开头的项
      const keysToRemove = [];
      for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i);
        if (key && (key.startsWith('searchState_') || key.startsWith('returnSearchState_'))) {
          keysToRemove.push(key);
        }
      }
      keysToRemove.forEach(key => sessionStorage.removeItem(key));

      console.log('已清空搜索缓存数据');
    } catch (error) {
      console.error('清空搜索缓存失败:', error);
    }

    history.push(`/${prefix}/search`);
  }, [history, prefix]);

  const toLibraryManagement = useCallback(() => {
    history.push(`/${prefix}/libraryManagement`);
  }, [history, prefix]);



  const { isDarkMode } = useTheme();

  useEffect(() => {
    videoWallHDMIGet().then(res => {
      if (res && res.code === 0 && res.data && res.data.list) {
        console.log('获取投屏种类', JSON.stringify(res.data.list));
        if (res.data.list.length > 0) {
          setHasHDMI(true);
          return;
        }
        // 获取投屏种类失败不做Toast提示
        // Toast.show('获取投屏种类失败');
      } else {
        console.log('获取投屏种类失败!', res && JSON.stringify(res));
        // Toast.show('获取投屏种类失败');
      }
    })
  }, [])

  // 投屏点击事件
  const hdmiOnClick = useCallback(async () => {
    window.onetrack?.('track', 'mediacenter_hdmiBtn_click');
    try {
      console.log('投屏按钮点击');
      await videoWallHDMIOnClick();
    } catch (e) {
      console.log('投屏按钮点击失败', e && JSON.stringify(e));
    }
  }, [])

  const rightSize = useMemo(() => {
    return (
      <div className={styles.right}>
        {hasHDMI && <PreloadImage src={isDarkMode ? hdmiIcon_dark : hdmiIcon} alt="screen casting" onClick={hdmiOnClick} />}
        <PreloadImage src={isDarkMode ? searchIcon_dark : searchIcon} alt="search" onClick={() => toSearch()} />
        {path === 'all' && (<PreloadImage src={isDarkMode ? filterIcon_dark : filterIcon} style={{width:'40px',height:'40px'}} alt="filter" onClick={() => setShowFloatPanel(true)} />)}

        <Popover
          className={styles.morePopoverContainer}
          visible={moreVisible}
          onVisibleChange={setMoreVisible}
          content={
            <div className={styles.morePopover}>
              <div className={styles.morePopoverItem} onClick={toLibraryManagement}>
                <span className={styles.morePopoverText}>媒体库管理</span>
              </div>
            </div>
          }
          trigger='click'
          placement='bottom-end'
          style={{ '--arrow-size': '0px' } as React.CSSProperties}
        >
          <PreloadImage src={isDarkMode ? moreIcon_dark : moreIcon} style={{width:'40px',height:'40px'}} alt="more" onClick={() => setMoreVisible(true)} />
        </Popover>
      </div>
    )
  }, [hasHDMI, isDarkMode, hdmiOnClick, path, moreVisible, toLibraryManagement, toSearch])

  return (
    <>
      <NavigatorBar notBack right={rightSize} />
      <div className={styles.container}>
        <div className={styles.tabsHeader}>
          <span className={`${styles.tabsHeader_span} ${path === 'recently' ? styles.path_active : ''}`} onClick={() => history.push(`/${prefix}/recently`)}>最近</span>
          <span className={`${styles.tabsHeader_span} ${path === 'all' ? styles.path_active : ''}`} onClick={() => history.push(`/${prefix}/all`)}>全部</span>
        </div>
        <div className={styles.tabsContent}>
          {path === 'recently' ? <Recently /> : <All setShowFloatPanel={setShowFloatPanel} showFloatPanel={showFloatPanel} />}
        </div>
      </div>
    </>
  );
}

export const useLibraryListApp = () => useContext(LibraryContext);

const FilmAndTelevisionWallAPP: FC = (props) => {
  const { path } = useRouteMatch();
  const { location } = useHistory();
  const currentPath = location.pathname.replace(path, '');

  const { libraries, getLib, setLibraries } = useVideoLibraryList();

  return (
    <LibraryContext.Provider value={{ libs: libraries, setLibs: setLibraries, refreshLibraries: (noIcon) => getLib(noIcon) as any }}>
      <div className={(currentPath === '/recently' || currentPath === '/all') ? styles.root_container : styles.root_subContainer}>
        {props.children}
        <Switch>
          {/* 默认内容 */}
          <Route exact={true} path={path}>
            <DefaultLayout />
          </Route>
        </Switch>
      </div>
    </LibraryContext.Provider>
  )
}

export default FilmAndTelevisionWallAPP;