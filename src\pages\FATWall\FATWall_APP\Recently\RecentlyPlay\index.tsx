import React, { useState, useRef, useEffect, useCallback } from "react";
import styles from "./index.module.scss";

import FilmCard from "../../../../../components/FATWall_APP/FilmCard";
import NavigatorBar from "@/components/NavBar";
import { useTheme } from "@/utils/themeDetector";
import arrowLeft from "@/Resources/icon/backIcon_light.png";
import arrowLeftDark from "@/Resources/icon/backIcon_dark.png";
import edit from "@/Resources/icon/edit.png";
import editDark from "@/Resources/icon/edit-dark.png";
import finish from "@/Resources/icon/finish.png";
import finishDark from "@/Resources/icon/finish-dark.png";
import deleted from "@/Resources/icon/delete.png";
import deleteDark from "@/Resources/icon/delete_white.png";
import close from "@/Resources/icon/close.png";
import closeDark from "@/Resources/icon/close_white.png";
import { useHistory } from "react-router-dom";
import { Checkbox, Toast } from "antd-mobile";
import { modalShow } from "@/components/List";
import { PreloadImage } from "@/components/Image";
import { useRequest } from 'ahooks';
import { getRecentlyWatched, mediaClearWatched, RecentlyPlayedFileInfo, getMediaFiles, MediaFileResponse } from "@/api/fatWall";
import { px2rem } from "@/utils/setRootFontSize";
import placeholder‌_poster from '@/Resources/icon/placeholder‌_row.png';
import { playVideo } from "@/api/fatWallJSBridge";


// 格式化时间的工具函数
export const formatTimeAgo = (timestamp: number): string => {
  if (!timestamp) return '';
  
  // 计算当前时间与传入时间戳的差值（秒）
  const now = Math.floor(Date.now() / 1000);
  const seconds = now - timestamp;
  
  const minutes = seconds / 60;
  const hours = minutes / 60;
  const days = hours / 24;
  
  if (minutes < 1) {
    return `${Math.floor(seconds)}秒`;
  } else if (minutes < 60) {
    return `${Math.floor(minutes)}分钟`;
  } else if (hours < 24) {
    return `${Math.floor(hours)}小时`;
  } else if (days < 7) {
    return `${Math.floor(days)}天`;
  } else if (days < 30) {
    return `${Math.floor(days / 7)}周`;
  } else if (days < 365) {
    return `${Math.floor(days / 30)}个月`;
  } else {
    return `${Math.floor(days / 365)}年`;
  }
};

export default function RecentlyPlay() {
  const { isDarkMode } = useTheme();
  const history = useHistory();
  const [isEditMode, setIsEditMode] = useState(false);
  const [offset, setOffset] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(false);
  const [initialLoaded, setInitialLoaded] = useState(false);
  const [currentPlayItem, setCurrentPlayItem] = useState<any>(null); // 当前要播放的项目

  // 最近播放列表
  const [recentlyPlayList, setRecentlyPlayList] = useState<(RecentlyPlayedFileInfo & { selected?: boolean })[]>([]);

  // 获取媒体文件列表的API调用
  const { run: runGetMediaFiles } = useRequest(
    getMediaFiles,
    {
      manual: true,
      onSuccess: (res: { code: number; data: MediaFileResponse }) => {
        if (res.code === 0 && res.data && res.data.files && currentPlayItem) {
          console.log('APP端RecentlyPlay获取到完整剧集列表:', res.data.files);
          handleStartPlay(res.data.files, currentPlayItem);
        }
      },
      onError: (error) => {
        console.error('APP端RecentlyPlay获取媒体文件列表失败:', error);
        Toast.show({
          content: '获取剧集列表失败',
          position: 'bottom',
          duration: 1500,
        });
      },
    }
  );

  // 请求最近播放接口
  const { run: fetchRecentlyWatched } = useRequest(
    (params) => getRecentlyWatched(params),
    {
      manual: true,
      onSuccess: (res) => {
        const newData = (res.data as any)?.files.map((item: RecentlyPlayedFileInfo) => ({ ...item, selected: false }));
        if (offset === 0) {
          setRecentlyPlayList(newData);
          setInitialLoaded(true);
          setHasMore(newData.length === 50);
        } else {
          setRecentlyPlayList(prev => [...prev, ...newData]);
          setHasMore(newData.length === 50);
        }
        setLoading(false);
      },
    }
  );

  // 处理开始播放
  const handleStartPlay = useCallback((mediaFiles: any[], playItem: any) => {
    if (!mediaFiles || mediaFiles.length === 0) {
      Toast.show({
        content: '暂无可播放的文件',
        position: 'bottom',
        duration: 1500,
      });
      return;
    }

    // 构建APP端videoList数组，参考VideoDetails中的handlePlay方法
    const videoList = mediaFiles.map(file => ({
      path: file.path,
      media_id: playItem.media_id?.toString() || '0',
      file_id: file.file_id.toString(),
      duration: file.duration || 0, // 视频时长
      position: file.last_play_point || 0, // 断点信息
      isCompelete: file.seen, // 是否完整播放，转换为boolean
      audioIndex: file.audio_index || 0, // 音轨信息，默认为0
      subtitlePath: file.subtitle_path || '', // 字幕路径
      subtitleType: file.subtitle_type || 0, // 字幕类型，0表示内嵌字幕
      subtitleIndex: file.subtitle_index || 0, // 字幕索引，默认为0
    }));

    // 通过当前项的file_id在列表中找到索引位置
    let playIndex = 0;
    if (playItem.file_id) {
      const targetIndex = mediaFiles.findIndex(file => file.file_id === playItem.file_id);
      if (targetIndex !== -1) {
        playIndex = targetIndex;
        console.log(`APP端RecentlyPlay播放：找到file_id(${playItem.file_id})对应的索引位置：${targetIndex}`);
      } else {
        console.log(`APP端RecentlyPlay播放：未找到file_id(${playItem.file_id})对应的文件，将播放第一集`);
      }
    }

    // 调用APP端视频播放接口
    playVideo(videoList, playIndex, (res) => {
      // if (res.code === 0) {
      //   Toast.show({
      //     content: '开始播放',
      //     position: 'bottom',
      //     duration: 1500,
      //   });
      // } else {
      //   Toast.show({
      //     content: `播放失败: ${res.msg}`,
      //     position: 'bottom',
      //     duration: 1500,
      //   });
      // }
    }).catch((error) => {
      Toast.show({
        content: error.message || '播放失败',
        position: 'bottom',
        duration: 1500,
      });
    });

    // 清空当前播放项目状态
    setCurrentPlayItem(null);
  }, []);

  // 处理卡片点击播放
  const handlePlayClick = useCallback((item: any) => {
    console.log('APP端RecentlyPlay点击播放卡片:', item);

    if (!item.media_id) {
      Toast.show({
        content: '缺少媒体ID，无法播放',
        position: 'bottom',
        duration: 1500,
      });
      return;
    }

    // 调用接口获取完整的剧集列表
    runGetMediaFiles({
      lib_id: 0, // 根据需求设置为0
      media_id: item.media_id
    });

    // 保存当前点击的项目信息，用于后续播放
    setCurrentPlayItem(item);
  }, [runGetMediaFiles]);

  useEffect(() => {
    fetchRecentlyWatched({ offset: 0, limit: 50 });
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const loadMore = useCallback(() => {
    if (!hasMore || loading || isEditMode) return;
    setLoading(true);
    const newOffset = offset + 50;
    setOffset(newOffset);
    fetchRecentlyWatched({ offset: newOffset, limit: 50 });
  }, [offset, hasMore, loading, isEditMode, fetchRecentlyWatched]);

  const { run: runMediaClearWatched } = useRequest(
    mediaClearWatched,
    {
      manual: true,
      onSuccess: (_, params) => {
        // 删除成功后更新状态
        const selectedIds = params[0].file_ids;
        const newList = recentlyPlayList.filter((item) => !selectedIds.includes(item.file_id));
        setRecentlyPlayList(newList);

        if (newList.length === 0) {
          history.push("/filmAndTelevisionWall_app/recently");
        }

        Toast.show({
          content: "删除成功",
          duration: 2000,
          position: "bottom",
        });
      },
      onError: () => {
        Toast.show({
          content: "删除失败，请重试",
          duration: 2000,
          position: "bottom",
        });
      },
    }
  );

  const toggleEditMode = () => {
    if (isEditMode) {
      setRecentlyPlayList(
        recentlyPlayList.map((item) => ({
          ...item,
          selected: false,
        }))
      );
    }
    setIsEditMode(!isEditMode);
  };

  const handleBack = () => {
    if (isEditMode) {
      setIsEditMode(false);
      setRecentlyPlayList(
        recentlyPlayList.map((item) => ({
          ...item,
          selected: false,
        }))
      );
    } else {
      history.goBack();
    }
  };

  const toggleItemSelection = (id: string) => {
    if (!isEditMode) return;

    setRecentlyPlayList(
      recentlyPlayList.map((item) =>
        item.file_id === Number(id) ? { ...item, selected: !item.selected } : item
      )
    );
  };

  const deleteSelectedItems = () => {
    const selectedItems = recentlyPlayList.filter((item) => item.selected);
    const selectedIds = selectedItems.map(item => item.file_id);

    modalShow(
      "确定删除观看记录？",
      <div className={styles.modalConfirmText}>{/* 确定删除观看记录？ */}</div>,
      (m) => {
        m.destroy();
        //状态更新和Toast显示在API回调中处理
        runMediaClearWatched({ file_ids: selectedIds });
      },
      () => {}, // onCancel
      false, // onlyShow
      {
        okBtnText: "删除",
        cancelBtnText: "取消",
        okBtnStyle: {
          color: "var(--emergency-text-color)",
          background: "var(--cancel-btn-background-color)",
        },
        position: "bottom"
      }
    );
  };

  const hasSelectedItems = recentlyPlayList.some((item) => item.selected);

  const arrowIcon = isDarkMode ? arrowLeftDark : arrowLeft;

  const closeIcon = isDarkMode ? closeDark : close;

  const finishIcon = isDarkMode ? finishDark : finish;

  const editIcon = isDarkMode ? editDark : edit;

  // 添加列表容器的引用
  const filmsContainerRef = useRef<HTMLDivElement>(null);
  
  // 监听编辑模式变化，重置滚动位置
  useEffect(() => {
    if (filmsContainerRef.current && !isEditMode) {
      filmsContainerRef.current.scrollTop = 0;
    }
  }, [isEditMode]);

  // 监听滚动事件
  useEffect(() => {
    const handleScroll = () => {
      if (!filmsContainerRef.current) return;
      
      const { scrollTop, scrollHeight, clientHeight } = filmsContainerRef.current;
      // 只有初始50条数据加载完成，并且滚动到底部时才加载更多
      if (initialLoaded && scrollHeight - scrollTop - clientHeight < 20 && hasMore && !loading && !isEditMode) {
        loadMore();
      }
    };

    const container = filmsContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
    }

    return () => {
      if (container) {
        container.removeEventListener('scroll', handleScroll);
      }
    };
  }, [hasMore, loading, isEditMode, loadMore, initialLoaded]);

  return (
    <div className={styles.container}>
      <NavigatorBar
        backIcon={isEditMode ? closeIcon : arrowIcon}
        backIconSize={isEditMode ? "26px" : "40px"}
        right={
          recentlyPlayList.length > 0 && (
            <PreloadImage
              src={isEditMode ? finishIcon : editIcon}
              alt="edit"
              onClick={toggleEditMode}
              style={{width:'40px',height:'40px'}}
            />
          )
        }
        onBack={handleBack}
      />
      <div className={styles.title}>{isEditMode ? "编辑" : "最近播放"}</div>
      
      <div className={styles.filmsContainer} ref={filmsContainerRef}>
          {recentlyPlayList.length > 0 ? (
            recentlyPlayList.map((item) => (
              <div key={item.file_id} className={styles.filmItem}>
                <FilmCard
                  poster={item.poster ? item.poster.length > 0 ? item.poster.length > 1 ? item.poster[1] : item.poster[0] : placeholder‌_poster : placeholder‌_poster}
                  title={item?.media_classes === "电视剧" ? `${item?.media_name}第${item?.episode}集` : item?.media_name || ''}
                  progress={item?.last_seen_percent}
                  time={formatTimeAgo(item.last_seen_time)}
                  type="play"
                  layout="horizontal"
                  options={{
                    style: { width: px2rem("121px"), height: px2rem("78px") },
                    callback: () =>
                      !isEditMode && handlePlayClick(item),
                  }}
                />
                {isEditMode && (
                  <Checkbox
                    checked={item.selected}
                    onChange={() => toggleItemSelection(item.file_id.toString())}
                    className={styles.antdCheckbox}
                  />
                )}
              </div>
            ))
          ) : (
            <div className={styles.noDataTip}>暂无数据</div>
          )}
        </div>
      
      {isEditMode && (
        <div className={styles.deleteContainer}>
          <div>
            <PreloadImage
              style={{width:25,height:25}}
              src={isDarkMode ? deleteDark : deleted}
              className={`${styles.deleteButton} ${
                !hasSelectedItems ? styles.disabled : ""
              }`}
              onClick={hasSelectedItems ? deleteSelectedItems : undefined}
            />
          </div>
          <div className={styles.deletdRecord}>删除记录</div>
        </div>
      )}
    </div>
  );
}
