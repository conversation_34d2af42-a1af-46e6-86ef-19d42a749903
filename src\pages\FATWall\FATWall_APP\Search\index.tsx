import { FC, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useHistory, useLocation } from "react-router-dom";
import { SearchOutline } from 'antd-mobile-icons';
import { <PERSON><PERSON>, ErrorBlock, Input } from "antd-mobile";
import FilterFilmCard from "@/components/FATWall_APP/FilterFilmCard";
import { searchLocalMedia } from "@/api/fatWall";
import { useTheme } from '@/utils/themeDetector';
import content_null_img from '@/Resources/icon/content_null.png';
import content_null_img_dark from '@/Resources/icon/content_null_dark.png';
import styles from "./index.module.scss";

// 搜索状态类型
type SearchStatus = 'idle' | 'loading' | 'success' | 'error' | 'empty';

// 搜索结果类型
interface SearchResult {
  id: string;
  title: string;
  year: string;
  country: string;
  genres: string;
  posterUrl: string;
  selected?: boolean;
  score: number;
  type: string;
  media_id: number;
  classes: string;
}

interface SearchPageProps {
  // 可选的初始搜索状态，用于从其他页面恢复搜索状态
  initialSearchState?: {
    searchKeyword?: string;
    searchCategory?: '全部' | '电影' | '电视剧';
    searchResults?: SearchResult[];
    searchStatus?: SearchStatus;
  };
}

const SearchPage: FC<SearchPageProps> = ({ initialSearchState }) => {
  const history = useHistory();
  const location = useLocation();
  const { isDarkMode } = useTheme();

  const searchInputRef = useRef<any>(null);
  const [searchKeyword, setSearchKeyword] = useState<string>(initialSearchState?.searchKeyword || '');
  const [searchStatus, setSearchStatus] = useState<SearchStatus>(initialSearchState?.searchStatus || 'idle');
  const [searchResults, setSearchResults] = useState<SearchResult[]>(initialSearchState?.searchResults || []);
  const [searchCategory, setSearchCategory] = useState<'全部' | '电影' | '电视剧'>(initialSearchState?.searchCategory || '全部');



  // 页面加载时聚焦搜索框和恢复搜索状态
  useEffect(() => {
    // 优先处理 URL 参数的状态恢复（正常返回）
    const urlParams = new URLSearchParams(location.search);
    const restoreSearch = urlParams.get('restoreSearch');
    const searchStateKey = urlParams.get('searchStateKey');

    if (restoreSearch === 'true' && searchStateKey) {
      // 正常的状态恢复逻辑（从 sessionStorage）
      try {
        const savedState = sessionStorage.getItem(searchStateKey);
        if (savedState) {
          const searchState = JSON.parse(savedState);
          setSearchKeyword(searchState.searchKeyword || '');
          setSearchCategory(searchState.searchCategory || '全部');
          setSearchResults(searchState.searchResults || []);
          setSearchStatus(searchState.searchStatus || 'idle');

          // 清除 sessionStorage 和 URL 参数
          sessionStorage.removeItem(searchStateKey);
          history.replace(location.pathname);

          // 聚焦搜索框
          setTimeout(() => {
            if (searchInputRef.current) {
              searchInputRef.current.focus();
            }
          }, 100);
          return; // 如果成功恢复，就不需要再检查 localStorage
        }
      } catch (error) {
        console.error('恢复搜索状态失败:', error);
      }
    }

    // 如果没有 URL 参数，尝试从 localStorage 恢复搜索状态（用于安卓手势返回）
    const savedSearchState = localStorage.getItem('fatwall_search_state');
    if (savedSearchState) {
      try {
        const searchState = JSON.parse(savedSearchState);
        // 检查状态是否在有效期内（5分钟）
        const now = Date.now();
        if (searchState.timestamp && (now - searchState.timestamp) < 5 * 60 * 1000) {
          setSearchKeyword(searchState.searchKeyword || '');
          setSearchCategory(searchState.searchCategory || '全部');
          setSearchResults(searchState.searchResults || []);
          setSearchStatus(searchState.searchStatus || 'idle');

          // 如果是从详情页返回的，清除标记
          if (searchState.fromDetailPage) {
            localStorage.removeItem('fatwall_search_state');
          }
        } else {
          // 过期则清除
          localStorage.removeItem('fatwall_search_state');
        }
      } catch (error) {
        console.error('恢复搜索状态失败:', error);
        localStorage.removeItem('fatwall_search_state');
      }
    }

    setTimeout(() => {
      if (searchInputRef.current) {
        searchInputRef.current.focus();
      }
    }, 100);
  }, [location.search, location.pathname, history]);

  // 监听搜索状态变化，自动保存到 localStorage
  useEffect(() => {
    // 只有当有搜索内容时才保存，并且确保不是在清空状态的过程中
    if (searchKeyword.trim() || searchResults.length > 0) {
      const searchState = {
        searchKeyword,
        searchCategory,
        searchResults,
        searchStatus,
        timestamp: Date.now()
      };
      localStorage.setItem('fatwall_search_state', JSON.stringify(searchState));
    } else if (!searchKeyword.trim() && searchResults.length === 0 && searchStatus === 'idle') {
      // 如果所有状态都被清空了，也清空 localStorage
      localStorage.removeItem('fatwall_search_state');
    }
  }, [searchKeyword, searchCategory, searchResults, searchStatus]);

  // 页面卸载时的清理（可选，用于精确控制）
  useEffect(() => {
    const handleBeforeUnload = () => {
      // 在页面卸载时保存当前状态
      if (searchKeyword.trim() || searchResults.length > 0) {
        const searchState = {
          searchKeyword,
          searchCategory,
          searchResults,
          searchStatus,
          timestamp: Date.now()
        };
        localStorage.setItem('fatwall_search_state', JSON.stringify(searchState));
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [searchKeyword, searchCategory, searchResults, searchStatus]);

  // 处理搜索
  const handleSearch = useCallback(async () => {
    if (searchKeyword.trim()) {
      setSearchStatus('loading');

      try {
        // 根据搜索分类确定接口参数
        let classes = '';
        if (searchCategory === '电影') {
          classes = '电影';
        } else if (searchCategory === '电视剧') {
          classes = '电视剧';
        }

        const response = await searchLocalMedia({
          keyword: searchKeyword.trim(),
          filter: {
            offset: 0,
            limit: 50,
            classes
          }
        });

        if (response.code === 0 && response.data) {
          const { medias, count } = response.data;

          if (count === 0) {
            setSearchStatus('empty');
            setSearchResults([]);
          } else {
            // 将接口返回的mediaProps数据转换为SearchResult格式
            const transformedResults: SearchResult[] = medias.map(media => ({
              id: media.media_id.toString(),
              title: media.trans_name || media.origin_name || media.other_name,
              year: media.year.toString(),
              country: media.origin_place,
              genres: media.kind.join(' / '),
              posterUrl: media.poster.length > 0 ? media.poster[0] : '',
              type: media.classes === '电视剧' ? '电视剧' : '电影',
              media_id: media.media_id,
              classes: media.classes,
              score: media.score ? media.score : 0,
            }));

            setSearchResults(transformedResults);
            setSearchStatus('success');
          }
        } else {
          setSearchStatus('error');
          setSearchResults([]);
        }
      } catch (error) {
        console.error('搜索失败:', error);
        setSearchStatus('error');
        setSearchResults([]);
      }
    }
  }, [searchKeyword, searchCategory]);

  // 处理重试
  const handleRetry = useCallback(() => {
    handleSearch();
  }, [handleSearch]);

  // 处理分类切换
  const handleCategoryChange = useCallback((category: '全部' | '电影' | '电视剧') => {
    setSearchCategory(category);
  }, []);

  // 当搜索分类改变且有搜索关键词时，重新搜索
  useEffect(() => {
    if (searchKeyword.trim() && (searchStatus === 'success' || searchStatus === 'empty' || searchStatus === 'error')) {
      handleSearch();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchCategory]); // 只监听searchCategory变化

  // 根据当前分类筛选结果 - 现在接口已经根据分类返回了过滤后的结果
  const filteredResults = useMemo(() => {
    return searchResults;
  }, [searchResults]);

  // 处理点击电影卡片
  const handleMovieCardClick = useCallback((item: any) => {
    console.log('搜索结果点击项:', item);

    // 将搜索状态保存到 sessionStorage，因为 Hash 路由不支持 state
    const searchStateKey = `searchState_${Date.now()}`;
    const searchState = {
      fromSearch: true,
      searchKeyword: searchKeyword,
      searchCategory: searchCategory,
      searchResults: searchResults,
      searchStatus: searchStatus
    };
    sessionStorage.setItem(searchStateKey, JSON.stringify(searchState));

    // 同时保存到 localStorage，用于安卓手势返回
    const persistentSearchState = {
      searchKeyword,
      searchCategory,
      searchResults,
      searchStatus,
      timestamp: Date.now(),
      fromDetailPage: true // 标记是从详情页返回的
    };
    localStorage.setItem('fatwall_search_state', JSON.stringify(persistentSearchState));

    // 设置一个标记，表明用户即将进入详情页
    localStorage.setItem('fatwall_entering_detail', 'true');

    // 将参数拼接到URL上，包括搜索状态的key
    const params = new URLSearchParams({
      classes: item.classes || '',
      media_id: (item.media_id || parseInt(item.id))?.toString() || '0',
      lib_id: '0',
      fromSearch: 'true',
      searchStateKey: searchStateKey // 添加搜索状态的key
    });

    // 跳转到详情页
    history.push(`/filmAndTelevisionWall_app/all/videoDetails?${params.toString()}`);
  }, [history, searchKeyword, searchCategory, searchResults, searchStatus]);

  // 清空所有搜索缓存的辅助函数
  const clearAllSearchCache = useCallback(() => {
    try {
      // 清空 localStorage 中的搜索状态
      localStorage.removeItem('fatwall_search_state');

      // 清空进入详情页的标记
      localStorage.removeItem('fatwall_entering_detail');

      // 清空所有可能的 sessionStorage 搜索状态
      // 遍历 sessionStorage，清除所有以 searchState_ 开头的项
      const keysToRemove = [];
      for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i);
        if (key && (key.startsWith('searchState_') || key.startsWith('returnSearchState_'))) {
          keysToRemove.push(key);
        }
      }
      keysToRemove.forEach(key => sessionStorage.removeItem(key));

      console.log('已清空搜索缓存数据');
    } catch (error) {
      console.error('清空搜索缓存失败:', error);
    }
  }, []);

  // 处理返回/取消
  const handleBack = useCallback(() => {
    // 清空所有搜索相关的缓存数据
    clearAllSearchCache();

    // 清空当前页面的搜索状态
    setSearchKeyword('');
    setSearchCategory('全部');
    setSearchResults([]);
    setSearchStatus('idle');

    // 检查是否有返回信息，决定返回到哪个页面
    try {
      const returnInfoStr = localStorage.getItem('fatwall_search_return_info');
      if (returnInfoStr) {
        const returnInfo = JSON.parse(returnInfoStr);
        // 检查返回信息是否在有效期内（5分钟）
        const now = Date.now();
        if (returnInfo.timestamp && (now - returnInfo.timestamp) < 5 * 60 * 1000) {
          // 清除返回信息
          localStorage.removeItem('fatwall_search_return_info');

          if (returnInfo.fromLibrary && returnInfo.libraryState) {
            // 返回到Library页面，恢复Library的状态
            console.log('Search: 返回到Library页面，传递state:', returnInfo.libraryState);

            // 确保localStorage中也有正确的state
            localStorage.setItem('filmAndTelevisionWall_app_library_state', JSON.stringify(returnInfo.libraryState));

            history.push({
              pathname: returnInfo.returnPath,
              state: returnInfo.libraryState
            });
            return;
          }
        } else {
          // 过期则清除
          localStorage.removeItem('fatwall_search_return_info');
        }
      }
    } catch (error) {
      console.error('处理返回信息失败:', error);
      localStorage.removeItem('fatwall_search_return_info');
    }

    // 默认返回到全部页面
    history.push('/filmAndTelevisionWall_app/all');
  }, [history, clearAllSearchCache]);

  // 渲染搜索结果内容
  const renderSearchContent = () => {
    switch (searchStatus) {
      case 'loading':
        return (
          <div className={styles.searchStateContainer}>
            <div className={styles.loadingContainer}>
              <div className={styles.loadingSpinner}></div>
              <div className={styles.loadingText}>搜索中...</div>
            </div>
          </div>
        );
      case 'error':
        return (
          <>
            {/* Tab切换区域固定显示 */}
            <div className={styles.search_category_tabs_fixed}>
              <div
                className={`${styles.category_tab} ${searchCategory === '全部' ? styles.active_tab : ''}`}
                onClick={() => handleCategoryChange('全部')}
              >
                全部
              </div>
              <div
                className={`${styles.category_tab} ${searchCategory === '电影' ? styles.active_tab : ''}`}
                onClick={() => handleCategoryChange('电影')}
              >
                电影
              </div>
              <div
                className={`${styles.category_tab} ${searchCategory === '电视剧' ? styles.active_tab : ''}`}
                onClick={() => handleCategoryChange('电视剧')}
              >
                电视剧
              </div>
            </div>
            {/* 可滚动的搜索结果区域 */}
            <div className={styles.search_results_scrollable}>
              <div className={styles.searchStateContainer}>
                <ErrorBlock
                  className={styles.errorBlock}
                  image={isDarkMode ? content_null_img_dark : content_null_img}
                  status="default"
                  title="搜索失败，请重试"
                  description=""
                />
                <Button
                  className={styles.retryButton}
                  color="primary"
                  fill="none"
                  onClick={handleRetry}
                >
                  重试
                </Button>
              </div>
            </div>
          </>
        );
      case 'empty':
        return (
          <>
            {/* Tab切换区域固定显示 */}
            <div className={styles.search_category_tabs_fixed}>
              <div
                className={`${styles.category_tab} ${searchCategory === '全部' ? styles.active_tab : ''}`}
                onClick={() => handleCategoryChange('全部')}
              >
                全部
              </div>
              <div
                className={`${styles.category_tab} ${searchCategory === '电影' ? styles.active_tab : ''}`}
                onClick={() => handleCategoryChange('电影')}
              >
                电影
              </div>
              <div
                className={`${styles.category_tab} ${searchCategory === '电视剧' ? styles.active_tab : ''}`}
                onClick={() => handleCategoryChange('电视剧')}
              >
                电视剧
              </div>
            </div>
            {/* 可滚动的搜索结果区域 */}
            <div className={styles.search_results_scrollable}>
              <div className={styles.searchStateContainer}>
                <ErrorBlock
                  className={styles.errorBlock}
                  image={isDarkMode ? content_null_img_dark : content_null_img}
                  status="empty"
                  title="没搜索到相关结果"
                  description=""
                />
              </div>
            </div>
          </>
        );
      case 'success':
        return (
          <>
            {/* Tab切换区域固定显示 */}
            <div className={styles.search_category_tabs_fixed}>
              <div
                className={`${styles.category_tab} ${searchCategory === '全部' ? styles.active_tab : ''}`}
                onClick={() => handleCategoryChange('全部')}
              >
                全部
              </div>
              <div
                className={`${styles.category_tab} ${searchCategory === '电影' ? styles.active_tab : ''}`}
                onClick={() => handleCategoryChange('电影')}
              >
                电影
              </div>
              <div
                className={`${styles.category_tab} ${searchCategory === '电视剧' ? styles.active_tab : ''}`}
                onClick={() => handleCategoryChange('电视剧')}
              >
                电视剧
              </div>
            </div>
            {/* 可滚动的搜索结果区域 */}
            <div className={styles.search_results_scrollable}>
              {filteredResults.length > 0 ? (
                <div className={styles.filter_films_container}>
                  {filteredResults.map(result => (
                    <FilterFilmCard
                      type='app'
                      isLike={false}
                      key={result.id}
                      title={result.title}
                      subtitle={result.year}
                      score={result.score}
                      cover={result.posterUrl}
                      onCardClick={() => handleMovieCardClick(result)}
                    />
                  ))}
                </div>
              ) : (
                /* 当前分类无数据时显示空状态，但保持tab可见 */
                <div className={styles.searchStateContainer}>
                  <ErrorBlock
                    className={styles.errorBlock}
                    image=''
                    status="empty"
                    title="没搜索到相关结果"
                    description=""
                  />
                </div>
              )}
            </div>
          </>
        );
      default:
        return null;
    }
  };

  return (
    <div className={styles.matchCorrectionOverlay}>
      <div className={styles.searchContainer}>
        <div className={styles.searchInputWrapper}>
          <div className={styles.searchIconWrapper}>
            <SearchOutline className={styles.searchIcon} />
          </div>
          <Input
            ref={searchInputRef}
            className={styles.searchInput}
            placeholder="请输入你要搜索的内容"
            value={searchKeyword}
            onChange={setSearchKeyword}
            onEnterPress={handleSearch}
            autoFocus
            clearable
          />
        </div>
        <div className={styles.cancelButton} onClick={handleBack}>
          取消
        </div>
      </div>
      <div className={styles.searchResults}>
        {renderSearchContent()}
      </div>
    </div>
  );
};

export default SearchPage;