import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import FilmCard, { IFilmCard } from "@/components/FATWall_APP/FilmCard";
import styles from './index.module.scss';
import useRowDragScroll from "@/hooks/useDragScroll";
import { px2rem } from "@/utils/setRootFontSize";
import selected from "@/Resources/icon/selected.png";
import notSelect from "@/Resources/icon/not_select.png";
import { PreloadImage } from "@/components/Image";
import refreshIcon from '@/Resources/icon/refreshBtn.png';
import refreshIcon_dark from '@/Resources/icon/refreshBtn_dark.png';
import { RecentlyPlayedResponse, getRecentlyWatched, mediaClearWatched, getMediaFiles, MediaFileResponse } from "@/api/fatWall";
import { useInViewport, useRequest, useUpdateEffect } from "ahooks";
import { playVideo } from "@/api/fatWallJSBridge";
import FATErrorComponents from "../../Error";
import { Toast } from "antd-mobile";
import { formatTimeAgo } from "@/pages/FATWall/FATWall_APP/Recently/RecentlyPlay";
import { Divider, message } from "antd";
import { useTheme } from "@/utils/themeDetector";
import { useLibraryList } from "../..";
import placeholder‌_poster from '@/Resources/icon/placeholder‌_row.png';
import { modalShow } from "@/components/List";

export const defaultPageParamPC = { offset: 0, limit: 20 };

const RowDragWrapper = (props: { item: IFilmCard[], selectCallback: (v: any) => void, selectList: IFilmCard[], onPlayClick: (item: IFilmCard) => void }) => {
  const { rowRef, handleMouseDown, isDragging } = useRowDragScroll();
  const [hoverKey, setHoverKey] = useState<string>('');

  // 选中
  const clickCallback = useCallback((it: IFilmCard) => {
    props.selectCallback((p: IFilmCard[]) => {
      let pr = [...p];
      const isIncludes: boolean = pr.includes(it);
      if (isIncludes) {
        pr = pr.filter(item => item !== it);
      } else {
        pr.push(it);
      }
      return pr;
    })
  }, [props])

  return (
    <div className={styles.film_cards_container} ref={rowRef} onMouseDown={handleMouseDown} style={{ cursor: isDragging ? 'grabbing' : 'grab' }}>
      {
        props.item.map((it) => (
          <div key={it.title} className={styles.film_card_container} style={{ background: props.selectList.includes(it) ? 'var(--fat-card-hover-bg)' : '' }} onMouseLeave={() => setHoverKey('')} onMouseEnter={() => setHoverKey(it.title)}>
            <FilmCard {...it} key={it.title} options={{
              style: { width: px2rem('170px'), height: px2rem('95px') },
              callback: () => props.onPlayClick(it) // 添加播放点击回调
            }} />
            <div className={styles.select_item} onClick={() => clickCallback(it)} style={{ visibility: (props.selectList.includes(it) || hoverKey === it.title) ? 'visible' : 'hidden' }}>
              {props.selectList.includes(it) ? <PreloadImage src={selected} alt="selected" /> : <PreloadImage src={notSelect} alt="notSelect" />}
            </div>
          </div>
        ))
      }
    </div>
  )
}

const RecentlyPlay = () => {
  const [selectList, setSelectList] = useState<IFilmCard[]>([]);
  const [recentlyPlayFilm, setRecentlyPlayFilm] = useState<RecentlyPlayedResponse>({ files: [], count: 0 });
  const [isError, setIsError] = useState<boolean>(false); // 查询是否出错，用于显示刷新按钮

  // 加载更多的必要参数
  const [pageOpt, setPageOpt] = useState<{ offset: number, limit: number }>(defaultPageParamPC); // 分页参数
  const prevParams = useRef({ ...pageOpt });
  const loaderRef = useRef<HTMLDivElement>(null);
  const [hasMore, setHasMore] = useState(false);

  // 最近播放和添加的电影列表数据
  const watchedRun = useCallback(async (callback: (v: RecentlyPlayedResponse) => void, pageOpt) => {
    const data = await getRecentlyWatched(pageOpt).catch(e => {
      console.log('获取最近播放失败：', e);
      setIsError(true);
    });

    if (data && data.code === 0 && data.data) {
      // 最近播放的电影列表数据加载成功，更新状态
      callback({ files: data.data.files, count: 0 });
      setIsError(false);
      // 判断是否还有更多数据可以加载
      if (data.data.count < pageOpt.limit) {
        setHasMore(false);
      } else {
        setHasMore(true);
      }
    } else {
      setIsError(true);
    }
  }, []);

  const libs = useLibraryList().libs;

  // 分页发生变化的时候请求数据
  useUpdateEffect(() => {
    watchedRun((v) => setRecentlyPlayFilm(p => { return { count: p.count + v.count, files: [...p.files, ...v.files] } }), pageOpt);
  }, [pageOpt, watchedRun])

  // 初始化获取数据
  useEffect(() => {
    watchedRun((v) => setRecentlyPlayFilm(p => ({ count: p.count, files: v.files })), prevParams.current);
  }, [watchedRun])

  const recentlyPlayFilmList: IFilmCard[] = useMemo(() => {
    return recentlyPlayFilm.files.map((media, index) => {
      const time = formatTimeAgo(media.last_seen_time || 0);
      const poster = media.poster ? media.poster.length > 0 ? media.poster.length > 1 ? media.poster[1] : media.poster[0] : placeholder‌_poster : placeholder‌_poster; // 数组0索引为封面图，1索引为海报
      return {
        ...media, poster: poster, progress: media.last_seen_percent,
        title: media.media_classes === '电视剧' ? `${media.media_name}第${media.episode}集` : media.media_name,
        time: `${time}`, media_id: media.media_id, type: 'play', duration: media.duration
      }
    })
  }, [recentlyPlayFilm])

  const recentlyPlayListByTime = useMemo(() => {
    let obj: { [key: string]: IFilmCard[] } = {
      sevenDays: [],
      oneMonths: [],
      threeMonths: []
    };

    recentlyPlayFilmList.forEach((it: any) => {
      const now = Math.floor(Date.now() / 1000);
      const seconds = now - it.last_seen_time;
      const minutes = seconds / 60;
      const hours = minutes / 60;
      const time = hours / 24; // 天数

      if (time <= 7) {
        obj['sevenDays'].push(it);
      } else if (time > 7 && time <= 31) {
        obj['oneMonths'].push(it);
      } else {
        obj['threeMonths'].push(it);
      }
    })
    return obj;
  }, [recentlyPlayFilmList])

  const timeLabel: { [key: string]: string } = {
    sevenDays: '近7天',
    oneMonths: '近1个月',
    threeMonths: '近3个月'
  }

  // 重置
  const clearAndRefresh = useCallback(() => {
    setHasMore(false);
    setSelectList([]);
    setRecentlyPlayFilm({ files: [], count: 0 });

    // 重置分页参数，重新加载数据
    setPageOpt(defaultPageParamPC);
    if (prevParams.current) {
      const { limit, offset } = prevParams.current;
      if (limit === defaultPageParamPC.limit && offset === defaultPageParamPC.offset) {
        watchedRun((v) => setRecentlyPlayFilm(v), defaultPageParamPC);
      }
    }
    prevParams.current = defaultPageParamPC;
  }, [watchedRun])

  const { run: runMediaClearWatched } = useRequest(
    mediaClearWatched,
    {
      manual: true,
      onSuccess: () => {
        Toast.show({
          content: "删除成功",
          duration: 2000,
          position: "bottom",
        });
        clearAndRefresh();
      },
      onError: () => {
        Toast.show({
          content: "删除失败，请重试",
          duration: 2000,
          position: "bottom",
        });
      },
    }
  );

  // 删除最近播放记录
  const delRcd = useCallback(() => {
    modalShow('确定删除观看记录？', <></>, (m) => {
      const file_ids = selectList.map((it) => it.file_id).filter((it) => it !== undefined);
      if (file_ids && file_ids.length > 0) {
        runMediaClearWatched({ file_ids: file_ids });
        m.destroy();
      }
    }, () => null, false, { position: 'bottom' });
  }, [runMediaClearWatched, selectList])

  const { isDarkMode } = useTheme();

  // 获取媒体文件列表的API调用
  const { run: runGetMediaFiles } = useRequest(
    getMediaFiles,
    {
      manual: true,
      onSuccess: (res: { code: number; data: MediaFileResponse }) => {
        if (res.code === 0 && res.data && res.data.files && currentPlayItem) {
          console.log('获取到完整剧集列表:', res.data.files);
          handleStartPlay(res.data.files, currentPlayItem);
        }
      },
      onError: (error) => {
        console.error('获取媒体文件列表失败:', error);
        message.error('获取剧集列表失败');
      },
    }
  );

  // 处理开始播放
  const handleStartPlay = useCallback((mediaFiles: any[], playItem: IFilmCard) => {
    if (!mediaFiles || mediaFiles.length === 0) {
      message.error('暂无可播放的文件');
      return;
    }

    // 构建PC端videoList数组
    const videoList = mediaFiles.map(file => ({
      path: file.path,
      media_id: playItem.media_id?.toString() || '0',
      file_id: file.file_id.toString(),
      total_time: file.duration || 0, // PC端使用total_time字段
      filter: {
        last_play_point: file.last_play_point,
        audio_index: file.audio_index,
        seen: file.seen,
        subtitle_path: file.subtitle_path,
        subtitle_type: file.subtitle_type,
        subtitle_index: file.subtitle_index,
      }
    }));

    // 通过当前项的file_id在列表中找到索引位置
    let playIndex = 0;
    if (playItem.file_id) {
      const targetIndex = mediaFiles.findIndex(file => file.file_id === playItem.file_id);
      if (targetIndex !== -1) {
        playIndex = targetIndex;
        console.log(`PC端RecentlyPlay播放：找到file_id(${playItem.file_id})对应的索引位置：${targetIndex}`);
      } else {
        console.log(`PC端RecentlyPlay播放：未找到file_id(${playItem.file_id})对应的文件，将播放第一集`);
      }
    }

    // 调用PC端视频播放接口
    playVideo(videoList, playIndex, (res) => {
      // if (res.code === 0) {
      //   message.success('开始播放');
      // } else {
      //   message.error(`播放失败: ${res.msg}`);
      // }
    }).catch((error) => {
      message.error(error.message || '播放失败');
    });

    // 清空当前播放项目状态
    setCurrentPlayItem(null);
  }, []);

  // 处理卡片点击播放
  const handlePlayClick = useCallback((item: IFilmCard) => {
    console.log('点击播放卡片:', item);

    if (!item.media_id) {
      message.error('缺少媒体ID，无法播放');
      return;
    }

    // 调用接口获取完整的剧集列表
    runGetMediaFiles({
      lib_id: 0, // 根据需求设置为0
      media_id: item.media_id
    });

    // 保存当前点击的项目信息，用于后续播放
    setCurrentPlayItem(item);
  }, [runGetMediaFiles]);

  // 添加状态存储当前要播放的项目
  const [currentPlayItem, setCurrentPlayItem] = useState<IFilmCard | null>(null);

  // 滚动到底部加载更多数据
  const [inViewport] = useInViewport(loaderRef, {
    threshold: 0.1,
  })

  useEffect(() => {
    if (inViewport) {
      setPageOpt((prev) => {
        prevParams.current = { ...prev, offset: prev.offset + prev.limit };
        return { ...prev, offset: prev.offset + prev.limit }
      })
    }
  }, [inViewport])

  return (
    <div className={styles.component_container}>
      <div className={styles.header}>
        <div className={styles.util_items}>
          {
            selectList.length > 0 && <div className={styles.operationItem}>
              <span style={{ color: 'rgba(255, 112, 113, 1)' }} onClick={delRcd}>删除记录</span>
            </div>
          }
          <PreloadImage src={isDarkMode ? refreshIcon_dark : refreshIcon}  style={{width:'40px',height:'40px'}}alt="refresh" onClick={clearAndRefresh} />
        </div>
      </div>
      <div className={styles.error_container}>
        <FATErrorComponents span={isError ? '获取失败' : '暂无内容'} canTry={isError} refresh={clearAndRefresh} show={isError || libs.length === 0 || recentlyPlayFilmList.length === 0} subSpan={libs.length > 0 && recentlyPlayFilmList.length === 0 ? '近三个月无记录' : undefined}>
          {
            Object.keys(recentlyPlayListByTime).map((key) => {
              if (recentlyPlayListByTime[key].length > 0) {
                return <div key={key}>
                  <div key={key} className={styles.container}>
                    <span className={styles.time_title}>{timeLabel[key]}</span>
                    <RowDragWrapper selectCallback={setSelectList} item={recentlyPlayListByTime[key]} selectList={selectList} onPlayClick={handlePlayClick} />
                  </div>
                  {
                    key === 'sevenDays' && (recentlyPlayListByTime['oneMonths'].length > 0 || recentlyPlayListByTime['threeMonths'].length > 0) ? <Divider style={{ width: `calc(100% - ${px2rem('56px')})`, margin: `${px2rem('8px')} auto` }} /> :
                      key === 'oneMonths' && recentlyPlayListByTime['threeMonths'].length > 0 ? <Divider style={{ width: `calc(100% - ${px2rem('56px')})`, margin: `${px2rem('8px')} auto` }} /> : <></>
                  }
                </div>
              }
              return null;
            })
          }

          {
            hasMore && <div ref={loaderRef} style={{ padding: '20px', height: '20px' }}></div>
          }
        </FATErrorComponents>
      </div>
    </div>
  )
}

export default RecentlyPlay;