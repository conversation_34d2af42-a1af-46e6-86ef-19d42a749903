/**
 * common utils part,please add more with mark details
 */

export default class CommonUtils {

  //分割字符串+单位，例如50GB=>50 GB  
  static splitNumberAndUnit(str) {
    const match = str.match(/^(\d+\.?\d*)(\D+)$/);
    if (!match) {
      throw new Error("无效的格式");
    }
    return {
      number: parseFloat(match[1]), // 转换为浮点数
      unit: match[2]
    };
  }

  /**
   * 格式化文件路径，将系统路径转换为用户友好的显示格式
   * @param {string} filePath - 原始文件路径
   * @returns {string} 格式化后的路径
   */
  static formatFilePath(filePath) {
    if (!filePath) {
      return '未知路径';
    }

    // 使用正则表达式匹配 /home/<USER>/pool0/data 部分
    const pattern = /\/home\/u\d+\/pool0\/data/i;
    const match = filePath.match(pattern);

    if (match) {
      // 找到匹配的部分并替换为"存储空间"
      return filePath.replace(match[0], '存储空间');
    }

    return filePath;
  }


  /**
     * 格式化时间戳为指定格式的日期字符串
     * @param {number} timestamp - 时间戳（毫秒）
     * @param {string} [format='YYYY-MM-DD'] - 日期格式
     * @returns {string} 格式化后的日期字符串
     */
  static formatTimestamp(timestamp, format = 'YYYY-MM-DD') {
    if (!timestamp) return '无效时间';

    // 自动检测时间戳是秒还是毫秒（10位秒级，13位毫秒级）
    const adjustedTimestamp = timestamp.toString().length <= 10 ? timestamp * 1000 : timestamp;

    const date = new Date(adjustedTimestamp);

    if (isNaN(date.getTime())) return '无效时间';

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return format
      .replace(/YYYY/g, year)
      .replace(/MM/g, month)
      .replace(/DD/g, day)
      .replace(/HH/g, hours)
      .replace(/mm/g, minutes)
      .replace(/ss/g, seconds);
  }
}
